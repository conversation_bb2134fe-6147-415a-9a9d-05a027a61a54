# B 题 网络切片无线资源管理方案设计

随着物联网设备的指数级增长,移动通信需求呈现爆发式增长,推动了网络架构的不断演进。为应对不同场景下的数据密度与服务需求,移动通信网络逐步采用宏基站与小基站混合部署的异构蜂窝网络结构,其中宏基站负责大范围的覆盖,微基站用于在高密度区域提供高效的无线接入。两层的异构网络可以提升网络容量、覆盖范围和用户体验,尤其是在 5G 及未来网络中,以支持大规模的设备接入和高数据传输速率。

*图 1. 蜂窝异构网络资源管理两步决策*

如图 1 所示,在部署一个宏基站和四个微基站的网络场景中,多个用户在任务到达后申请下行传输,系统将用户分配给基站,基站根据用户需求分配资源块,接着进行数据传输,传输速率的计算方式见附录(1.信号传输模型)。

在此基础上,5G 引入网络切片技术,通过网络功能虚拟化手段将物理网络划分为多个逻辑独立的切片,例如高可靠低时延切片(Ultra-Reliable and Low-Latency Communications, URLLC)、增强移动宽带切片(Enhanced Mobile Broadband, eMBB ) 和 大 规 模 机 器 通 信 切 片 (Massive Machine Type Communications, mMTC),使同一基础设施能够灵活适配工业控制、超高清视频、 大规模感知等多样化业务需求。具体的,通过正交频分多址接入技术将基站的总频谱从时域和频域划分为多个资源块,并将这些资源块灵活分配给所有的切片。假设资源块在频域上的跨度为 360kHZ,在时域上的跨度为 10ms,具体技术原理见附录(2.正交频分多址接入技术)。面对多切片并存、异构网络协同的复杂场景,如何实现无线资源的高效分配,成为保障网络性能与服务质量的关键问题。

为了能更好的优化无线网络对用户的服务质量,需要根据不同用户的特异化服务质量偏好去评估系统资源分配模型的性能表现,针对每个用户任务的服务质量评估模型见附录(3.用户服务质量定义)的说明。在长时间的通信服务过程中,用户的下行传输任务总是以一定的概率到达,同时用户还存在一定的移动性,因此用户的接入模式通常也会变化,因此为了更灵活的服务基站范围内的用户,假设系统每 100 ms 决策一次系统的资源配置方案。

根据以上研究背景,请参赛队伍解决以下问题:

### 问题一

现存在一个微基站向其覆盖范围内的用户提供服务,该基站拥有 50 个资源块用于分配。假设当下用户只到达了一个任务,将所有的资源块分配给三类切片,用于响应用户的下行传输需求,详细数据见附件 1。用户任务在基站中的处理过程见附录(4.用户任务服务流程)请给出具体的资源块分配方案, 使用户服务质量达到最大。

### 问题二

现实情况中,用户通常会在一段时间内以概率进行多次传输请求,且用户会进行移动,导致信道发生变化,因此系统在每隔一段时间对资源重新进行分配,分配时不仅要考虑即将到达的任务,还需要考虑积压在排队队列中的用户任务。附件 2 中给出了所有用户在 1000 ms 内的任务到达情况和用户与基站之间的信道信息,中间需要对资源进行 10 次决策,请给出每次决策的三类切片的资源块最佳分配方案,使得整体的用户服务质量达到最大。

### 问题三

针对多个微基站的情况,为了提升频谱利用率,不同基站通常采用相同的频谱资源进行下行传输,因此不同的传输链路之间可能存在干扰,基站之间的干扰模式在附录中(5.信号干扰模型)进行了说明。为了对干扰进行控制, 提升用户体验,基站在对资源块进行分配的同时,还需要对每个基站的发射功率进行控制,以降低系统整体的干扰程度。 在附件 3 中给出了所有用户的任务到达情况,以及所有用户在每一时刻与各个基站之间的信道情况。功率决策决定了每个切片内所有资源块统一的功率, 基站功率的取值范围为 p ∈ 10,30 dBm。请给出各个基站每次决策的资源块分配方案和功率控制方案,使系统的用户服务质量达到最大。

### 问题四

为了进一步提升系统的容量,通常设置一个宏基站用于全局的覆盖,且有着更充裕的频谱资源,同时设置多个微基站负责对边缘地区增强覆盖。其中宏基站和微基站的频谱不重叠,因此两类基站之间不存在干扰,仅微基站之间存在干扰。尽管宏基站有更好的服务性能,但无法同时满足所有用户的需求,因此在异构网络中,需要确定用户的接入模式,即每个用户是由最近的微基站服务还是由宏基站服务。 假定宏基站持有 100 个资源块,功率范围为 ∈ 10,40 dBm,和周围多个拥有 50 个资源块的微基站共同服务周边的用户,附件 4 给出了 1000 ms 内所有用户的任务到达概率以及用户与基站之间的信道信息。针对所有用户的任务需求, 请给出每个用户的接入决策,每个基站的切片划分决策以及基站的发射功率分配,以达到最大用户服务质量。

### 问题五

运营商能耗控制是经济与环保双重考量的核心:基站电费占据网络运营成本的核心,功率优化可降低能耗,直接减少电费与设备维护支出。通过平衡服务质量与能效,运营商既可缓解成本压力、延长设备寿命,又能履行绿色社会责任,构建技术驱动型可持续发展网络。在第四问的模型中,针对用户的任务情况以最大用户服务质量进行了资源调度,在本问中,能耗计算模型见附录(6.能耗模型),请基于第四问的数据给出合理的资源分配策略,使得能耗最低的同时能够达到最大的用户服务质量。
