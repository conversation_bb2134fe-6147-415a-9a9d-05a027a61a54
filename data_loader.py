# data_loader.py - Data Loading and Preprocessing for Q2
"""
Load and preprocess channel data and task arrivals from Excel file
"""
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import config

class Q2DataLoader:
    """Load and manage Q2 data from Excel file"""
    
    def __init__(self, excel_path: str = config.DATA_PATH):
        self.excel_path = Path(excel_path)
        self.sheets = {}
        self.users = []
        self.channel_data = {}
        self.task_arrivals = {}
        
    def load_all_sheets(self) -> Dict[str, pd.DataFrame]:
        """Load all sheets from Excel file"""
        if not self.excel_path.exists():
            raise FileNotFoundError(f"Excel file not found: {self.excel_path}")
            
        try:
            # Read all sheets
            self.sheets = pd.read_excel(self.excel_path, sheet_name=None, engine="openpyxl")
            
            # Clean column names
            for name, df in self.sheets.items():
                df.columns = [str(c).strip() for c in df.columns]
                self.sheets[name] = df.dropna(how="all")
                
            print(f"Loaded {len(self.sheets)} sheets:")
            for name, df in self.sheets.items():
                print(f"  - {name}: shape={df.shape}")
                
            return self.sheets
            
        except Exception as e:
            raise RuntimeError(f"Failed to load Excel file: {e}")
    
    def parse_users(self) -> List[str]:
        """Extract user list from sheet names or columns"""
        users = set()
        
        # Try to find users from sheet names or column headers
        for sheet_name, df in self.sheets.items():
            # Look for user patterns in columns
            for col in df.columns:
                col_str = str(col).upper()
                if col_str.startswith('U') and col_str[1:].isdigit():  # URLLC users
                    users.add(col)
                elif col_str.startswith('E') and col_str[1:].isdigit():  # eMBB users
                    users.add(col)
                elif col_str.startswith('M') and col_str[1:].isdigit():  # mMTC users
                    users.add(col)
        
        self.users = sorted(list(users))
        print(f"Found {len(self.users)} users: {self.users}")
        return self.users
    
    def get_user_type(self, user_id: str) -> str:
        """Determine user type from user ID"""
        user_upper = user_id.upper()
        if user_upper.startswith('U'):
            return 'URLLC'
        elif user_upper.startswith('E'):
            return 'eMBB'
        elif user_upper.startswith('M'):
            return 'mMTC'
        else:
            raise ValueError(f"Unknown user type for {user_id}")
    
    def parse_channel_data(self) -> Dict:
        """Parse channel information (path loss, small scale fading)"""
        channel_data = {}
        
        # Look for sheets containing channel information
        for sheet_name, df in self.sheets.items():
            if 'channel' in sheet_name.lower() or 'path' in sheet_name.lower():
                # Assume time is in first column or index
                if 'time' in df.columns[0].lower() or 'Time' in df.columns[0]:
                    time_col = df.columns[0]
                    times = df[time_col].values
                else:
                    times = df.index.values
                
                # Extract channel data for each user
                for user in self.users:
                    if user in df.columns:
                        channel_data[user] = {
                            'times': times,
                            'values': df[user].values
                        }
        
        self.channel_data = channel_data
        return channel_data
    
    def parse_task_arrivals(self) -> Dict:
        """Parse task arrival information"""
        task_arrivals = {}
        
        # Look for task flow sheets (mentioned as "人物流" in readme)
        task_sheet_names = ['人物流', '任务流', 'taskflow', 'task_flow', 'arrivals']
        
        for sheet_name, df in self.sheets.items():
            if any(name in sheet_name.lower() for name in task_sheet_names):
                # Parse task arrivals
                if 'time' in df.columns[0].lower():
                    time_col = df.columns[0]
                    times = df[time_col].values
                else:
                    times = df.index.values
                
                for user in self.users:
                    if user in df.columns:
                        task_arrivals[user] = {
                            'times': times,
                            'arrivals': df[user].values
                        }
        
        self.task_arrivals = task_arrivals
        return task_arrivals
    
    def get_channel_at_time(self, user_id: str, time_ms: float) -> Tuple[float, float]:
        """Get channel information for user at specific time"""
        # Convert time_ms to seconds for Excel data lookup
        time_s = time_ms / 1000.0

        # Get path loss from 大规模衰减 sheet
        path_loss_db = 80.0  # Default high path loss
        small_scale_linear = 1.0  # Default small scale fading

        # Find path loss sheet
        path_loss_sheet = None
        small_scale_sheet = None
        for name, df in self.sheets.items():
            if '大规模' in name:
                path_loss_sheet = df
            elif '小规模' in name:
                small_scale_sheet = df

        # Get path loss
        if path_loss_sheet is not None and user_id in path_loss_sheet.columns:
            times = path_loss_sheet['Time'].values
            values = path_loss_sheet[user_id].values
            if len(times) > 1:
                path_loss_db = np.interp(time_s, times, values)
            else:
                path_loss_db = values[0] if len(values) > 0 else 80.0

        # Get small scale fading (convert from dB to linear)
        if small_scale_sheet is not None and user_id in small_scale_sheet.columns:
            times = small_scale_sheet['Time'].values
            values = small_scale_sheet[user_id].values
            if len(times) > 1:
                small_scale_db = np.interp(time_s, times, values)
            else:
                small_scale_db = values[0] if len(values) > 0 else 0.0
            # Convert from dB to linear scale
            small_scale_linear = 10 ** (small_scale_db / 10)

        return path_loss_db, small_scale_linear
    
    def get_task_arrivals_at_time(self, user_id: str, time_ms: float) -> float:
        """Get task size for user at specific time (if task arrives)"""
        # Convert time_ms to seconds for Excel data lookup
        time_s = time_ms / 1000.0

        # Find task flow sheet
        task_sheet = None
        for name, df in self.sheets.items():
            if '任务流' in name or 'task' in name.lower():
                task_sheet = df
                break

        if task_sheet is None or user_id not in task_sheet.columns:
            return 0.0

        times = task_sheet['Time'].values
        task_sizes = task_sheet[user_id].values

        if len(times) > 1:
            task_size = np.interp(time_s, times, task_sizes)
        else:
            task_size = task_sizes[0] if len(task_sizes) > 0 else 0.0

        return max(0.0, task_size)
    
    def load_and_parse(self):
        """Complete data loading and parsing pipeline"""
        print("Loading Q2 data...")
        self.load_all_sheets()
        self.parse_users()
        self.parse_channel_data()
        self.parse_task_arrivals()
        print("Data loading complete.")
        
        return {
            'users': self.users,
            'channel_data': self.channel_data,
            'task_arrivals': self.task_arrivals
        }
