# data_loader.py - Data Loading and Preprocessing for Q2
"""
Load and preprocess channel data and task arrivals from Excel file
"""
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import config

class Q2DataLoader:
    """Load and manage Q2 data from Excel file"""
    
    def __init__(self, excel_path: str = config.DATA_PATH):
        self.excel_path = Path(excel_path)
        self.sheets = {}
        self.users = []
        self.channel_data = {}
        self.task_arrivals = {}
        
    def load_all_sheets(self) -> Dict[str, pd.DataFrame]:
        """Load all sheets from Excel file"""
        if not self.excel_path.exists():
            raise FileNotFoundError(f"Excel file not found: {self.excel_path}")
            
        try:
            # Read all sheets
            self.sheets = pd.read_excel(self.excel_path, sheet_name=None, engine="openpyxl")
            
            # Clean column names
            for name, df in self.sheets.items():
                df.columns = [str(c).strip() for c in df.columns]
                self.sheets[name] = df.dropna(how="all")
                
            print(f"Loaded {len(self.sheets)} sheets:")
            for name, df in self.sheets.items():
                print(f"  - {name}: shape={df.shape}")
                
            return self.sheets
            
        except Exception as e:
            raise RuntimeError(f"Failed to load Excel file: {e}")
    
    def parse_users(self) -> List[str]:
        """Extract user list from sheet names or columns"""
        users = set()
        
        # Try to find users from sheet names or column headers
        for sheet_name, df in self.sheets.items():
            # Look for user patterns in columns
            for col in df.columns:
                col_str = str(col).upper()
                if col_str.startswith('U') and col_str[1:].isdigit():  # URLLC users
                    users.add(col)
                elif col_str.startswith('E') and col_str[1:].isdigit():  # eMBB users
                    users.add(col)
                elif col_str.startswith('M') and col_str[1:].isdigit():  # mMTC users
                    users.add(col)
        
        self.users = sorted(list(users))
        print(f"Found {len(self.users)} users: {self.users}")
        return self.users
    
    def get_user_type(self, user_id: str) -> str:
        """Determine user type from user ID"""
        user_upper = user_id.upper()
        if user_upper.startswith('U'):
            return 'URLLC'
        elif user_upper.startswith('E'):
            return 'eMBB'
        elif user_upper.startswith('M'):
            return 'mMTC'
        else:
            raise ValueError(f"Unknown user type for {user_id}")
    
    def parse_channel_data(self) -> Dict:
        """Parse channel information (path loss, small scale fading)"""
        channel_data = {}
        
        # Look for sheets containing channel information
        for sheet_name, df in self.sheets.items():
            if 'channel' in sheet_name.lower() or 'path' in sheet_name.lower():
                # Assume time is in first column or index
                if 'time' in df.columns[0].lower() or 'Time' in df.columns[0]:
                    time_col = df.columns[0]
                    times = df[time_col].values
                else:
                    times = df.index.values
                
                # Extract channel data for each user
                for user in self.users:
                    if user in df.columns:
                        channel_data[user] = {
                            'times': times,
                            'values': df[user].values
                        }
        
        self.channel_data = channel_data
        return channel_data
    
    def parse_task_arrivals(self) -> Dict:
        """Parse task arrival information"""
        task_arrivals = {}
        
        # Look for task flow sheets (mentioned as "人物流" in readme)
        task_sheet_names = ['人物流', '任务流', 'taskflow', 'task_flow', 'arrivals']
        
        for sheet_name, df in self.sheets.items():
            if any(name in sheet_name.lower() for name in task_sheet_names):
                # Parse task arrivals
                if 'time' in df.columns[0].lower():
                    time_col = df.columns[0]
                    times = df[time_col].values
                else:
                    times = df.index.values
                
                for user in self.users:
                    if user in df.columns:
                        task_arrivals[user] = {
                            'times': times,
                            'arrivals': df[user].values
                        }
        
        self.task_arrivals = task_arrivals
        return task_arrivals
    
    def get_channel_at_time(self, user_id: str, time_ms: float) -> Tuple[float, float]:
        """Get channel information for user at specific time"""
        if user_id not in self.channel_data:
            # Return default values if no data
            return -80.0, 1.0  # path_loss_db, small_scale_linear
        
        data = self.channel_data[user_id]
        times = data['times']
        values = data['values']
        
        # Interpolate if needed
        if len(times) > 1:
            path_loss = np.interp(time_ms, times, values)
        else:
            path_loss = values[0] if len(values) > 0 else -80.0
            
        # For simplicity, assume small scale fading is 1.0 (can be enhanced)
        small_scale = 1.0
        
        return path_loss, small_scale
    
    def get_task_arrivals_at_time(self, user_id: str, time_ms: float) -> float:
        """Get task arrival probability/count for user at specific time"""
        if user_id not in self.task_arrivals:
            return 0.0
        
        data = self.task_arrivals[user_id]
        times = data['times']
        arrivals = data['arrivals']
        
        if len(times) > 1:
            arrival_prob = np.interp(time_ms, times, arrivals)
        else:
            arrival_prob = arrivals[0] if len(arrivals) > 0 else 0.0
            
        return max(0.0, arrival_prob)
    
    def load_and_parse(self):
        """Complete data loading and parsing pipeline"""
        print("Loading Q2 data...")
        self.load_all_sheets()
        self.parse_users()
        self.parse_channel_data()
        self.parse_task_arrivals()
        print("Data loading complete.")
        
        return {
            'users': self.users,
            'channel_data': self.channel_data,
            'task_arrivals': self.task_arrivals
        }
