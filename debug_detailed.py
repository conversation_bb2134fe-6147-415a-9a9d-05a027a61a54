#!/usr/bin/env python3
# debug_detailed.py - 详细调试任务完成情况
import numpy as np
from data_loader import Q2DataLoader
from simulator import Q2Simulator
from policies import FixedRatioPolicy, GreedyPolicy
import config

def debug_task_completion():
    """详细调试任务完成情况"""
    print("=== 详细调试任务完成情况 ===")
    
    loader = Q2DataLoader()
    loader.load_and_parse()
    
    # 测试两种不同的策略
    policies = {
        'Fixed': FixedRatioPolicy(),
        'Greedy': GreedyPolicy()
    }
    
    for policy_name, policy in policies.items():
        print(f"\n--- {policy_name} 策略详细分析 ---")
        
        simulator = Q2Simulator(loader)
        simulator.current_time_ms = 0.0
        
        total_tasks_arrived = 0
        total_tasks_completed = 0
        
        for decision_step in range(3):  # 只分析前3个决策周期
            decision_time = decision_step * config.DECISION_INTERVAL_MS
            print(f"\n决策 {decision_step} (时间 {decision_time}ms):")
            
            # 检查任务到达
            new_tasks = simulator.generate_task_arrivals_at_decision_time(decision_time)
            print(f"  新到达任务: {len(new_tasks)}")
            for task in new_tasks:
                print(f"    {task.user_id}: {task.data_size_mbit:.4f} Mbit")
                simulator.user_queues[task.user_id].add_task(task)
                total_tasks_arrived += 1
            
            # 检查队列状态
            state = simulator.get_queue_state()
            total_queue = sum(s['queue_length'] for s in state.values())
            print(f"  总队列长度: {total_queue}")
            
            # 做决策
            allocation = policy.make_decision(state, decision_time, loader)
            print(f"  资源分配: {allocation}")
            
            # 检查容量
            capacity = simulator.get_slice_capacity(allocation)
            print(f"  并发容量: {capacity}")
            
            # 仿真这个周期
            completed_before = len(simulator.completed_tasks)
            period_qos = simulator.simulate_decision_period(allocation)
            completed_after = len(simulator.completed_tasks)
            
            period_completed = completed_after - completed_before
            total_tasks_completed += period_completed
            
            print(f"  本周期完成任务: {period_completed}")
            print(f"  本周期QoS: {period_qos:.3f}")
            
            # 显示完成的任务详情
            if period_completed > 0:
                recent_completed = simulator.completed_tasks[completed_before:completed_after]
                print(f"  完成任务详情:")
                for task in recent_completed:
                    user_type = loader.get_user_type(task.user_id)
                    print(f"    {task.user_id}({user_type}): 大小{task.data_size_mbit:.4f}Mbit, 延迟{task.delay_ms:.1f}ms")
        
        print(f"\n{policy_name} 策略总结:")
        print(f"  总到达任务: {total_tasks_arrived}")
        print(f"  总完成任务: {total_tasks_completed}")
        print(f"  完成率: {total_tasks_completed/total_tasks_arrived*100:.1f}%")

def debug_qos_calculation():
    """调试QoS计算"""
    print("\n=== 调试QoS计算 ===")
    
    from rate_model import QoSCalculator
    qos_calc = QoSCalculator()
    
    # 测试各种QoS计算情况
    test_cases = [
        # URLLC测试
        {"type": "URLLC", "rate": 15.0, "delay": 3.0, "desc": "URLLC正常"},
        {"type": "URLLC", "rate": 8.0, "delay": 8.0, "desc": "URLLC超时"},
        
        # eMBB测试
        {"type": "eMBB", "rate": 60.0, "delay": 50.0, "desc": "eMBB满足SLA"},
        {"type": "eMBB", "rate": 30.0, "delay": 50.0, "desc": "eMBB部分满足"},
        {"type": "eMBB", "rate": 30.0, "delay": 150.0, "desc": "eMBB超时"},
        
        # mMTC测试
        {"type": "mMTC", "rate": 2.0, "delay": 300.0, "served": 3, "total": 5, "desc": "mMTC接入比例60%"},
        {"type": "mMTC", "rate": 2.0, "delay": 600.0, "served": 3, "total": 5, "desc": "mMTC超时"},
    ]
    
    for case in test_cases:
        if case["type"] == "mMTC":
            qos = qos_calc.calculate_mmtc_qos(
                case["served"], case["total"], case["delay"],
                config.USER_TYPES["mMTC"]["sla_delay_ms"],
                config.USER_TYPES["mMTC"]["penalty"]
            )
        else:
            qos = qos_calc.calculate_task_qos(case["type"], case["rate"], case["delay"])
        
        print(f"{case['desc']}: QoS = {qos:.3f}")

def debug_rate_calculation():
    """调试速率计算"""
    print("\n=== 调试速率计算 ===")
    
    from rate_model import RateCalculator
    calc = RateCalculator()
    
    # 使用实际的信道数据测试
    loader = Q2DataLoader()
    loader.load_and_parse()
    
    test_time = 0.0  # 测试时间0ms
    
    for user_id in ['U1', 'e1', 'm1']:
        user_type = loader.get_user_type(user_id)
        path_loss, small_scale = loader.get_channel_at_time(user_id, test_time)
        
        rb_per_user = config.USER_TYPES[user_type]['rb_per_user']
        rate = calc.calculate_user_rate(user_id, user_type, path_loss, small_scale, rb_per_user)
        
        print(f"{user_id}({user_type}): 路径损耗{path_loss:.1f}dB, 小尺度{small_scale:.3f}, 速率{rate:.3f}Mbps")

if __name__ == '__main__':
    debug_task_completion()
    debug_qos_calculation()
    debug_rate_calculation()
