#!/usr/bin/env python3
# debug_simulation.py - Debug Q2 simulation to identify QoS issues
"""
Debug version to analyze why QoS is low
"""
import numpy as np
import pandas as pd
from data_loader import Q2DataLoader
from simulator import Q2Simulator
from policies import FixedRatioPolicy
import config

def debug_data_loading():
    """Debug data loading and parsing"""
    print("=== DEBUGGING DATA LOADING ===")
    
    loader = Q2DataLoader()
    loader.load_all_sheets()
    loader.parse_users()
    
    # Check task arrival patterns
    print("\n📊 TASK ARRIVAL ANALYSIS:")
    task_sheet = None
    for name, df in loader.sheets.items():
        if '任务流' in name or 'task' in name.lower():
            task_sheet = df
            break
    
    if task_sheet is not None:
        print(f"Task sheet shape: {task_sheet.shape}")
        print("Sample task arrivals:")
        for user in ['U1', 'U2', 'e1', 'e2', 'm1', 'm2']:
            if user in task_sheet.columns:
                user_tasks = task_sheet[user].values
                non_zero = user_tasks[user_tasks > 0]
                print(f"  {user}: {len(non_zero)} arrivals, max={non_zero.max():.4f}, avg={non_zero.mean():.4f}")
    
    # Check channel conditions
    print("\n📡 CHANNEL CONDITIONS:")
    path_loss_sheet = None
    for name, df in loader.sheets.items():
        if '大规模' in name or 'path' in name.lower():
            path_loss_sheet = df
            break
    
    if path_loss_sheet is not None:
        print("Path loss ranges (dB):")
        for user in ['U1', 'U2', 'e1', 'e2', 'm1', 'm2']:
            if user in path_loss_sheet.columns:
                values = path_loss_sheet[user].values
                print(f"  {user}: {values.min():.1f} to {values.max():.1f} dB")
    
    return loader

def debug_rate_calculation():
    """Debug rate calculation with actual data"""
    print("\n=== DEBUGGING RATE CALCULATION ===")
    
    from rate_model import RateCalculator
    calc = RateCalculator()
    
    # Test with typical values from the data
    test_cases = [
        {"user": "U1", "path_loss": 50.0, "small_scale": 1.0, "rb": 10},
        {"user": "e1", "path_loss": 40.0, "small_scale": 1.0, "rb": 5},
        {"user": "m1", "path_loss": 45.0, "small_scale": 1.0, "rb": 2},
    ]
    
    for case in test_cases:
        sinr = calc.calculate_sinr(case["user"], case["path_loss"], case["small_scale"], case["rb"])
        rate = calc.calculate_rate_mbps(sinr, case["rb"])
        print(f"{case['user']}: Path loss={case['path_loss']}dB, SINR={sinr:.3f}, Rate={rate:.3f} Mbps")

def debug_task_generation():
    """Debug task generation process"""
    print("\n=== DEBUGGING TASK GENERATION ===")
    
    loader = Q2DataLoader()
    loader.load_and_parse()
    
    # Test task generation at different times
    test_times = [0, 100, 200, 300, 400, 500]
    
    for time_ms in test_times:
        print(f"\nTime {time_ms}ms:")
        total_arrivals = 0
        for user_id in loader.users:
            arrival_prob = loader.get_task_arrivals_at_time(user_id, time_ms / 1000.0)  # Convert to seconds
            if arrival_prob > 0:
                print(f"  {user_id}: arrival_prob={arrival_prob:.4f}")
                total_arrivals += arrival_prob
        print(f"  Total expected arrivals: {total_arrivals:.3f}")

def debug_qos_calculation():
    """Debug QoS calculation"""
    print("\n=== DEBUGGING QOS CALCULATION ===")
    
    from rate_model import QoSCalculator
    qos_calc = QoSCalculator()
    
    # Test QoS for different scenarios
    test_scenarios = [
        {"type": "URLLC", "rate": 15.0, "delay": 3.0, "desc": "Good URLLC"},
        {"type": "URLLC", "rate": 8.0, "delay": 8.0, "desc": "Bad URLLC (timeout)"},
        {"type": "eMBB", "rate": 60.0, "delay": 50.0, "desc": "Good eMBB"},
        {"type": "eMBB", "rate": 30.0, "delay": 50.0, "desc": "Partial eMBB"},
        {"type": "eMBB", "rate": 30.0, "delay": 150.0, "desc": "Bad eMBB (timeout)"},
        {"type": "mMTC", "rate": 2.0, "delay": 300.0, "desc": "Good mMTC"},
    ]
    
    for scenario in test_scenarios:
        qos = qos_calc.calculate_task_qos(scenario["type"], scenario["rate"], scenario["delay"])
        print(f"{scenario['desc']}: QoS = {qos:.3f}")

def debug_simulation_step_by_step():
    """Debug simulation step by step"""
    print("\n=== DEBUGGING SIMULATION STEP BY STEP ===")
    
    loader = Q2DataLoader()
    loader.load_and_parse()
    
    simulator = Q2Simulator(loader)
    policy = FixedRatioPolicy()
    
    # Run just first few decisions with detailed logging
    simulator.current_time_ms = 0.0
    
    for decision_step in range(3):  # Only first 3 decisions
        decision_time = decision_step * config.DECISION_INTERVAL_MS
        print(f"\n--- Decision {decision_step} at {decision_time}ms ---")
        
        # Add new arrivals
        period_end_time = decision_time + config.DECISION_INTERVAL_MS
        new_tasks = simulator.generate_task_arrivals_for_period(decision_time, period_end_time)
        print(f"New arrivals: {len(new_tasks)}")
        for task in new_tasks:
            print(f"  {task.user_id}: {task.data_size_mbit:.4f} Mbit")
            simulator.user_queues[task.user_id].add_task(task)
        
        # Get state
        state = simulator.get_queue_state()
        total_queue = sum(s['queue_length'] for s in state.values())
        print(f"Total queue length: {total_queue}")
        
        # Make decision
        allocation = policy.make_decision(state, decision_time, loader)
        print(f"Allocation: {allocation}")
        
        # Simulate period with detailed logging
        period_qos = 0.0
        for subframe in range(config.SUBFRAMES_PER_DECISION):
            subframe_start = simulator.current_time_ms
            
            # Get capacity
            capacity = simulator.get_slice_capacity(allocation)
            print(f"  Subframe {subframe}: capacity={capacity}")
            
            # Check serving users
            for slice_type in ['URLLC', 'eMBB', 'mMTC']:
                if capacity[slice_type] > 0:
                    serving_users = simulator.select_users_for_service(slice_type, capacity[slice_type])
                    if serving_users:
                        print(f"    {slice_type}: serving {serving_users}")
                        
                        for user_id in serving_users:
                            queue = simulator.user_queues[user_id]
                            task = queue.get_next_task()
                            if task:
                                # Get channel
                                path_loss, small_scale = loader.get_channel_at_time(user_id, simulator.current_time_ms / 1000.0)
                                
                                # Calculate rate
                                rb_per_user = config.USER_TYPES[slice_type]['rb_per_user']
                                rate = simulator.rate_calculator.calculate_user_rate(
                                    user_id, slice_type, path_loss, small_scale, rb_per_user
                                )
                                
                                print(f"      {user_id}: path_loss={path_loss:.1f}dB, rate={rate:.3f}Mbps, remaining={task.remaining_data_mbit:.4f}Mbit")
            
            subframe_qos = simulator.simulate_subframe(allocation)
            period_qos += subframe_qos
            print(f"  Subframe {subframe} QoS: {subframe_qos:.3f}")
            
        print(f"Period QoS: {period_qos:.3f}")

def main():
    """Main debug function"""
    print("🔍 Q2 SIMULATION DEBUG ANALYSIS")
    print("=" * 50)
    
    # Debug each component
    loader = debug_data_loading()
    debug_rate_calculation()
    debug_task_generation()
    debug_qos_calculation()
    debug_simulation_step_by_step()
    
    print("\n" + "=" * 50)
    print("🎯 DEBUG ANALYSIS COMPLETE")

if __name__ == '__main__':
    main()
