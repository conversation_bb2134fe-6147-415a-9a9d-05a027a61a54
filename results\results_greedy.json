{"policy": "Greedy", "total_qos": 3.0, "completed_tasks": 3, "qos_history": [0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0], "decision_summary": [{"time_ms": 0, "allocation": {"URLLC": 50, "eMBB": 0, "mMTC": 0}, "qos": 0.0, "total_queue_length": 0}, {"time_ms": 100, "allocation": {"URLLC": 50, "eMBB": 0, "mMTC": 0}, "qos": 0.0, "total_queue_length": 0}, {"time_ms": 200, "allocation": {"URLLC": 50, "eMBB": 0, "mMTC": 0}, "qos": 0.0, "total_queue_length": 0}, {"time_ms": 300, "allocation": {"URLLC": 50, "eMBB": 0, "mMTC": 0}, "qos": 0.0, "total_queue_length": 0}, {"time_ms": 400, "allocation": {"URLLC": 0, "eMBB": 0, "mMTC": 50}, "qos": 1.0, "total_queue_length": 1}, {"time_ms": 500, "allocation": {"URLLC": 0, "eMBB": 50, "mMTC": 0}, "qos": 1.0, "total_queue_length": 1}, {"time_ms": 600, "allocation": {"URLLC": 0, "eMBB": 50, "mMTC": 0}, "qos": 1.0, "total_queue_length": 1}, {"time_ms": 700, "allocation": {"URLLC": 50, "eMBB": 0, "mMTC": 0}, "qos": 0.0, "total_queue_length": 0}, {"time_ms": 800, "allocation": {"URLLC": 50, "eMBB": 0, "mMTC": 0}, "qos": 0.0, "total_queue_length": 0}, {"time_ms": 900, "allocation": {"URLLC": 50, "eMBB": 0, "mMTC": 0}, "qos": 0.0, "total_queue_length": 0}]}