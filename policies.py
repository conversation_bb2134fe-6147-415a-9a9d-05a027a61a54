# policies.py - Decision Policies for Resource Allocation
"""
Implement different decision policies: Greedy, MPC, etc.
"""
import numpy as np
from typing import Dict, List, Tuple
from abc import ABC, abstractmethod
import config

class DecisionPolicy(ABC):
    """Abstract base class for decision policies"""
    
    @abstractmethod
    def make_decision(self, state: Dict, time_ms: float, data_loader) -> Dict[str, int]:
        """Make resource allocation decision"""
        pass

class GreedyPolicy(DecisionPolicy):
    """Simple greedy policy based on queue lengths and urgency"""
    
    def __init__(self):
        self.name = "Greedy"
    
    def make_decision(self, state: Dict, time_ms: float, data_loader) -> Dict[str, int]:
        """Allocate RBs greedily based on queue urgency"""
        
        # Count users and urgency by slice type
        slice_stats = {'URLLC': [], 'eMBB': [], 'mMTC': []}
        
        for user_id, user_state in state.items():
            user_type = user_state['user_type']
            if user_state['queue_length'] > 0:
                urgency = self.calculate_urgency(user_state, user_type)
                slice_stats[user_type].append({
                    'user_id': user_id,
                    'urgency': urgency,
                    'queue_length': user_state['queue_length']
                })
        
        # Sort by urgency within each slice
        for slice_type in slice_stats:
            slice_stats[slice_type].sort(key=lambda x: x['urgency'], reverse=True)
        
        # Allocate RBs based on weighted urgency and minimum requirements
        allocation = self.allocate_rbs_greedy(slice_stats)
        
        return allocation
    
    def calculate_urgency(self, user_state: Dict, user_type: str) -> float:
        """Calculate urgency score for a user"""
        if user_state['queue_length'] == 0:
            return 0.0
        
        # Base urgency from queue length
        urgency = user_state['queue_length']
        
        # Add age factor
        age_factor = user_state['next_task_age'] / 1000  # Convert to seconds
        urgency += age_factor
        
        # Type-specific urgency multipliers
        type_multipliers = {'URLLC': 3.0, 'eMBB': 2.0, 'mMTC': 1.0}
        urgency *= type_multipliers.get(user_type, 1.0)
        
        return urgency
    
    def allocate_rbs_greedy(self, slice_stats: Dict) -> Dict[str, int]:
        """Allocate RBs greedily among slices"""
        allocation = {'URLLC': 0, 'eMBB': 0, 'mMTC': 0}
        remaining_rb = config.TOTAL_RB
        
        # First, ensure minimum allocation for urgent users
        for slice_type in ['URLLC', 'eMBB', 'mMTC']:  # Priority order
            users_with_tasks = slice_stats[slice_type]
            if not users_with_tasks:
                continue
            
            rb_per_user = config.USER_TYPES[slice_type]['rb_per_user']
            
            # Allocate for most urgent users first
            urgent_users = min(len(users_with_tasks), remaining_rb // rb_per_user)
            rb_needed = urgent_users * rb_per_user
            
            if rb_needed <= remaining_rb:
                allocation[slice_type] = rb_needed
                remaining_rb -= rb_needed
        
        # Distribute remaining RBs proportionally to total urgency
        if remaining_rb > 0:
            total_urgency = {}
            for slice_type, users in slice_stats.items():
                total_urgency[slice_type] = sum(u['urgency'] for u in users)
            
            total_urgency_sum = sum(total_urgency.values())
            
            if total_urgency_sum > 0:
                for slice_type in allocation:
                    if total_urgency[slice_type] > 0:
                        proportion = total_urgency[slice_type] / total_urgency_sum
                        additional_rb = int(remaining_rb * proportion)
                        
                        # Round to multiples of rb_per_user
                        rb_per_user = config.USER_TYPES[slice_type]['rb_per_user']
                        additional_rb = (additional_rb // rb_per_user) * rb_per_user
                        
                        allocation[slice_type] += additional_rb
        
        # Ensure total doesn't exceed limit
        total_allocated = sum(allocation.values())
        if total_allocated > config.TOTAL_RB:
            # Proportionally reduce
            factor = config.TOTAL_RB / total_allocated
            for slice_type in allocation:
                allocation[slice_type] = int(allocation[slice_type] * factor)
        
        # Fill remaining RBs if any
        remaining = config.TOTAL_RB - sum(allocation.values())
        if remaining > 0:
            # Give to URLLC first, then eMBB, then mMTC
            for slice_type in ['URLLC', 'eMBB', 'mMTC']:
                if remaining <= 0:
                    break
                rb_per_user = config.USER_TYPES[slice_type]['rb_per_user']
                can_add = (remaining // rb_per_user) * rb_per_user
                allocation[slice_type] += can_add
                remaining -= can_add
        
        return allocation

class MPCPolicy(DecisionPolicy):
    """Model Predictive Control policy with look-ahead"""
    
    def __init__(self, horizon: int = config.MPC_HORIZON):
        self.name = f"MPC-H{horizon}"
        self.horizon = horizon
    
    def make_decision(self, state: Dict, time_ms: float, data_loader) -> Dict[str, int]:
        """Make decision using MPC with prediction horizon"""
        
        # For simplicity, use enumeration over feasible allocations
        best_allocation = None
        best_predicted_qos = float('-inf')
        
        # Generate candidate allocations
        candidates = self.generate_allocation_candidates()
        
        for allocation in candidates:
            predicted_qos = self.predict_qos(allocation, state, time_ms, data_loader)
            
            if predicted_qos > best_predicted_qos:
                best_predicted_qos = predicted_qos
                best_allocation = allocation
        
        return best_allocation if best_allocation else {'URLLC': 20, 'eMBB': 20, 'mMTC': 10}
    
    def generate_allocation_candidates(self) -> List[Dict[str, int]]:
        """Generate feasible RB allocation candidates"""
        candidates = []
        
        # Simple enumeration with step size
        step = 5  # Step size for enumeration
        
        for urllc_rb in range(0, config.TOTAL_RB + 1, step):
            for embb_rb in range(0, config.TOTAL_RB - urllc_rb + 1, step):
                mmtc_rb = config.TOTAL_RB - urllc_rb - embb_rb
                
                if mmtc_rb >= 0:
                    # Ensure allocations are multiples of rb_per_user
                    urllc_rb = (urllc_rb // config.USER_TYPES['URLLC']['rb_per_user']) * config.USER_TYPES['URLLC']['rb_per_user']
                    embb_rb = (embb_rb // config.USER_TYPES['eMBB']['rb_per_user']) * config.USER_TYPES['eMBB']['rb_per_user']
                    mmtc_rb = (mmtc_rb // config.USER_TYPES['mMTC']['rb_per_user']) * config.USER_TYPES['mMTC']['rb_per_user']
                    
                    if urllc_rb + embb_rb + mmtc_rb <= config.TOTAL_RB:
                        candidates.append({
                            'URLLC': urllc_rb,
                            'eMBB': embb_rb,
                            'mMTC': mmtc_rb
                        })
        
        return candidates
    
    def predict_qos(self, allocation: Dict[str, int], state: Dict, time_ms: float, data_loader) -> float:
        """Predict QoS for given allocation over horizon"""
        
        # Simplified prediction: estimate based on current queue state and allocation
        predicted_qos = 0.0
        
        for slice_type in ['URLLC', 'eMBB', 'mMTC']:
            rb_allocated = allocation[slice_type]
            rb_per_user = config.USER_TYPES[slice_type]['rb_per_user']
            
            if rb_allocated == 0:
                continue
            
            # Count users of this type with tasks
            users_with_tasks = [uid for uid, ustate in state.items() 
                              if ustate['user_type'] == slice_type and ustate['queue_length'] > 0]
            
            if not users_with_tasks:
                continue
            
            # Estimate service capacity
            capacity = rb_allocated // rb_per_user
            served_users = min(capacity, len(users_with_tasks))
            
            # Estimate QoS based on slice type
            if slice_type == 'URLLC':
                # URLLC benefits from low delay
                avg_delay = max(1, sum(state[uid]['next_task_age'] for uid in users_with_tasks) / len(users_with_tasks))
                predicted_qos += served_users * config.USER_TYPES['URLLC']['alpha'] ** (avg_delay / 1000)
            
            elif slice_type == 'eMBB':
                # eMBB benefits from high rate
                # Simplified: assume rate proportional to RB allocation
                rate_factor = min(1.0, rb_allocated / (len(users_with_tasks) * rb_per_user))
                predicted_qos += served_users * rate_factor
            
            elif slice_type == 'mMTC':
                # mMTC benefits from access ratio
                access_ratio = served_users / len(users_with_tasks) if users_with_tasks else 0
                predicted_qos += access_ratio
        
        return predicted_qos

class FixedRatioPolicy(DecisionPolicy):
    """Fixed ratio allocation policy for baseline comparison"""

    def __init__(self, urllc_ratio: float = 0.4, embb_ratio: float = 0.4, mmtc_ratio: float = 0.2):
        self.name = f"Fixed-{urllc_ratio:.1f}-{embb_ratio:.1f}-{mmtc_ratio:.1f}"
        self.ratios = {'URLLC': urllc_ratio, 'eMBB': embb_ratio, 'mMTC': mmtc_ratio}

        # Normalize ratios
        total_ratio = sum(self.ratios.values())
        for slice_type in self.ratios:
            self.ratios[slice_type] /= total_ratio

    def make_decision(self, state: Dict, time_ms: float, data_loader) -> Dict[str, int]:
        """Allocate RBs with fixed ratios"""
        allocation = {}

        for slice_type, ratio in self.ratios.items():
            rb_per_user = config.USER_TYPES[slice_type]['rb_per_user']
            target_rb = int(config.TOTAL_RB * ratio)

            # Round to multiples of rb_per_user
            allocation[slice_type] = (target_rb // rb_per_user) * rb_per_user

        # Adjust for exact total
        total_allocated = sum(allocation.values())
        remaining = config.TOTAL_RB - total_allocated

        if remaining > 0:
            # Add remaining to URLLC
            allocation['URLLC'] += remaining

        return allocation

class OptimizedPolicy(DecisionPolicy):
    """Optimized policy based on data analysis"""

    def __init__(self):
        self.name = "Optimized"
        # Based on data analysis: eMBB has most tasks, mMTC second, URLLC least
        self.base_ratios = {'URLLC': 0.2, 'eMBB': 0.6, 'mMTC': 0.2}

    def make_decision(self, state: Dict, time_ms: float, data_loader) -> Dict[str, int]:
        """Make optimized allocation decision"""

        # Count active users by type
        active_users = {'URLLC': 0, 'eMBB': 0, 'mMTC': 0}
        urgency_scores = {'URLLC': 0, 'eMBB': 0, 'mMTC': 0}

        for user_id, user_state in state.items():
            user_type = user_state['user_type']
            if user_state['queue_length'] > 0:
                active_users[user_type] += 1

                # Calculate urgency based on queue length and age
                urgency = user_state['queue_length']
                age_factor = user_state['next_task_age'] / 1000  # Convert to seconds

                # Type-specific urgency weights
                if user_type == 'URLLC':
                    urgency += age_factor * 5  # High urgency for delay
                elif user_type == 'eMBB':
                    urgency += age_factor * 2  # Medium urgency
                else:  # mMTC
                    urgency += age_factor * 1  # Low urgency

                urgency_scores[user_type] += urgency

        # Calculate allocation based on active users and urgency
        allocation = {'URLLC': 0, 'eMBB': 0, 'mMTC': 0}

        # First, allocate minimum for active users
        for slice_type in ['URLLC', 'eMBB', 'mMTC']:
            if active_users[slice_type] > 0:
                rb_per_user = config.USER_TYPES[slice_type]['rb_per_user']
                min_allocation = min(active_users[slice_type], 3) * rb_per_user  # Cap at 3 users per slice
                allocation[slice_type] = min_allocation

        # Distribute remaining RBs based on urgency
        total_allocated = sum(allocation.values())
        remaining_rb = config.TOTAL_RB - total_allocated

        if remaining_rb > 0:
            total_urgency = sum(urgency_scores.values())

            if total_urgency > 0:
                for slice_type in allocation:
                    if urgency_scores[slice_type] > 0:
                        proportion = urgency_scores[slice_type] / total_urgency
                        additional_rb = int(remaining_rb * proportion)

                        # Round to multiples of rb_per_user
                        rb_per_user = config.USER_TYPES[slice_type]['rb_per_user']
                        additional_rb = (additional_rb // rb_per_user) * rb_per_user

                        allocation[slice_type] += additional_rb
            else:
                # No urgency, use base ratios
                for slice_type, ratio in self.base_ratios.items():
                    additional_rb = int(remaining_rb * ratio)
                    rb_per_user = config.USER_TYPES[slice_type]['rb_per_user']
                    additional_rb = (additional_rb // rb_per_user) * rb_per_user
                    allocation[slice_type] += additional_rb

        # Final adjustment to ensure total is exactly TOTAL_RB
        total_allocated = sum(allocation.values())
        if total_allocated < config.TOTAL_RB:
            remaining = config.TOTAL_RB - total_allocated
            # Give remaining to the slice with highest urgency
            if urgency_scores['URLLC'] > 0:
                allocation['URLLC'] += remaining
            elif urgency_scores['eMBB'] > 0:
                allocation['eMBB'] += remaining
            else:
                allocation['mMTC'] += remaining

        return allocation
