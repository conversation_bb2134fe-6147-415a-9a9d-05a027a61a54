{"policy": "Fixed-0.4-0.4-0.2", "total_qos": 4.0, "completed_tasks": 4, "qos_history": [0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0, 0.0, 1.0, 0.0], "decision_summary": [{"time_ms": 0, "allocation": {"URLLC": 20, "eMBB": 20, "mMTC": 10}, "qos": 0.0, "total_queue_length": 0}, {"time_ms": 100, "allocation": {"URLLC": 20, "eMBB": 20, "mMTC": 10}, "qos": 0.0, "total_queue_length": 0}, {"time_ms": 200, "allocation": {"URLLC": 20, "eMBB": 20, "mMTC": 10}, "qos": 0.0, "total_queue_length": 0}, {"time_ms": 300, "allocation": {"URLLC": 20, "eMBB": 20, "mMTC": 10}, "qos": 1.0, "total_queue_length": 1}, {"time_ms": 400, "allocation": {"URLLC": 20, "eMBB": 20, "mMTC": 10}, "qos": 0.0, "total_queue_length": 0}, {"time_ms": 500, "allocation": {"URLLC": 20, "eMBB": 20, "mMTC": 10}, "qos": 1.0, "total_queue_length": 1}, {"time_ms": 600, "allocation": {"URLLC": 20, "eMBB": 20, "mMTC": 10}, "qos": 1.0, "total_queue_length": 1}, {"time_ms": 700, "allocation": {"URLLC": 20, "eMBB": 20, "mMTC": 10}, "qos": 0.0, "total_queue_length": 0}, {"time_ms": 800, "allocation": {"URLLC": 20, "eMBB": 20, "mMTC": 10}, "qos": 1.0, "total_queue_length": 1}, {"time_ms": 900, "allocation": {"URLLC": 20, "eMBB": 20, "mMTC": 10}, "qos": 0.0, "total_queue_length": 0}]}