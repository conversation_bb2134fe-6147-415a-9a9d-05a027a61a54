# Q2 Dynamic Resource Allocation Solver

This Python solution implements a dynamic resource allocation system for the Q2 problem, featuring multiple decision policies and comprehensive simulation capabilities.

## Features

- **Data Loading**: Robust Excel data loader that handles multiple sheets and user types
- **Simulation Engine**: Complete system simulator with FIFO queuing, subframe-level transmission, and QoS calculation
- **Multiple Policies**: 
  - Greedy Policy (urgency-based allocation)
  - MPC Policy (Model Predictive Control with look-ahead)
  - Fixed Ratio Policy (baseline comparison)
- **Comprehensive Analysis**: Detailed results, visualizations, and policy comparisons

## Quick Start

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Run All Policies**:
   ```bash
   python main.py
   ```

3. **Run Specific Policy**:
   ```bash
   python main.py --policy greedy
   python main.py --policy mpc
   python main.py --policy fixed
   ```

4. **Custom Data File**:
   ```bash
   python main.py --data path/to/your/data.xlsx
   ```

## File Structure

- `config.py`: Configuration parameters and constants
- `data_loader.py`: Excel data loading and preprocessing
- `rate_model.py`: Signal transmission and QoS calculation models
- `simulator.py`: Main simulation engine with queuing and subframe logic
- `policies.py`: Decision policy implementations
- `main.py`: Main script and CLI interface

## Configuration

Key parameters can be modified in `config.py`:

- **Time Parameters**: Total simulation time (1000ms), decision intervals (100ms), subframes (10ms)
- **Resource Parameters**: Total RBs (50), RB bandwidth (360 kHz)
- **User Types**: URLLC/eMBB/mMTC parameters from Appendix Table 1
- **MPC Parameters**: Prediction horizon, optimization settings

## Policies

### Greedy Policy
- Allocates RBs based on queue urgency and user type priority
- Considers queue length, task age, and type-specific multipliers
- Fast execution, good baseline performance

### MPC Policy
- Model Predictive Control with configurable look-ahead horizon
- Enumerates feasible allocations and predicts future QoS
- Better long-term performance but higher computational cost

### Fixed Ratio Policy
- Allocates RBs with fixed proportions (default: 40% URLLC, 40% eMBB, 20% mMTC)
- Useful for baseline comparison and understanding policy impact

## Output

Results are saved in the `results/` directory:

- **JSON Files**: Detailed numerical results for each policy
- **PNG Files**: Visualization plots showing:
  - QoS over time
  - RB allocation patterns
  - Cumulative performance
  - Queue dynamics
- **Comparison Plots**: Multi-policy performance comparison

## Key Metrics

- **Total QoS**: Sum of all user service quality scores
- **Completed Tasks**: Number of successfully completed tasks
- **QoS Efficiency**: QoS per resource block per decision
- **SLA Compliance**: Adherence to delay and rate requirements

## Customization

### Adding New Policies
1. Inherit from `DecisionPolicy` in `policies.py`
2. Implement `make_decision()` method
3. Add to policy dictionary in `main.py`

### Modifying QoS Functions
- Edit `QoSCalculator` class in `rate_model.py`
- Adjust URLLC, eMBB, and mMTC QoS calculations

### Extending Data Loading
- Modify `Q2DataLoader` in `data_loader.py`
- Add support for additional Excel sheet formats

## Technical Details

### Simulation Flow
1. **Decision Period** (100ms): Make RB allocation decision
2. **Subframe Simulation** (10 × 10ms): Execute transmission with FIFO scheduling
3. **QoS Calculation**: Evaluate completed tasks and update metrics
4. **State Update**: Update queues and prepare for next decision

### Rate Calculation
Based on Appendix.md specifications:
- SINR calculation with path loss and small-scale fading
- Shannon capacity formula: `r = i × b × log₂(1 + γ)`
- Noise power includes thermal noise and receiver noise figure

### QoS Models
- **URLLC**: Exponential decay with delay, penalty for SLA violation
- **eMBB**: Rate-based scoring with delay constraints
- **mMTC**: Access ratio optimization with delay penalties

## Troubleshooting

### Common Issues
1. **Excel File Not Found**: Ensure `Solution/Q2/channel_data.xlsx` exists
2. **Import Errors**: Install dependencies with `pip install -r requirements.txt`
3. **Memory Issues**: Reduce MPC horizon or enumeration step size in `policies.py`

### Performance Optimization
- Adjust MPC horizon (default: 3) for speed vs. accuracy trade-off
- Modify enumeration step size in `generate_allocation_candidates()`
- Use fixed ratio policy for quick baseline results

## References

- Topic.md: Problem statement and requirements
- Appendix.md: Technical specifications and QoS definitions
- Q2.md & Q2explain.md: Detailed problem analysis and solution approach
