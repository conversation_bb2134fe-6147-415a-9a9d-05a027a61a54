#!/usr/bin/env python3
# analyze_results.py - Analyze and summarize Q2 results
"""
Analyze simulation results and provide summary statistics
"""
import json
import pandas as pd
from pathlib import Path
import matplotlib.pyplot as plt
import numpy as np

def load_results(results_dir="results"):
    """Load all result files"""
    results_dir = Path(results_dir)
    results = {}
    
    for json_file in results_dir.glob("results_*.json"):
        with open(json_file, 'r') as f:
            data = json.load(f)
            results[data['policy']] = data
    
    return results

def create_summary_table(results):
    """Create summary comparison table"""
    summary_data = []
    
    for policy, data in results.items():
        summary_data.append({
            'Policy': policy,
            'Total QoS': data['total_qos'],
            'Completed Tasks': data['completed_tasks'],
            'Avg QoS per Decision': data['total_qos'] / 10,
            'QoS Efficiency': data['total_qos'] / (50 * 10),  # QoS per RB per decision
            'Max Single QoS': max(data['qos_history']),
            'QoS Std Dev': np.std(data['qos_history'])
        })
    
    df = pd.DataFrame(summary_data)
    df = df.sort_values('Total QoS', ascending=False)
    return df

def analyze_allocation_patterns(results):
    """Analyze RB allocation patterns"""
    allocation_analysis = {}
    
    for policy, data in results.items():
        decisions = data['decision_summary']
        
        # Calculate average allocation per slice
        urllc_avg = np.mean([d['allocation']['URLLC'] for d in decisions])
        embb_avg = np.mean([d['allocation']['eMBB'] for d in decisions])
        mmtc_avg = np.mean([d['allocation']['mMTC'] for d in decisions])
        
        # Calculate allocation variance (how much it changes)
        urllc_std = np.std([d['allocation']['URLLC'] for d in decisions])
        embb_std = np.std([d['allocation']['eMBB'] for d in decisions])
        mmtc_std = np.std([d['allocation']['mMTC'] for d in decisions])
        
        allocation_analysis[policy] = {
            'avg_allocation': {'URLLC': urllc_avg, 'eMBB': embb_avg, 'mMTC': mmtc_avg},
            'allocation_std': {'URLLC': urllc_std, 'eMBB': embb_std, 'mMTC': mmtc_std},
            'adaptivity': urllc_std + embb_std + mmtc_std  # Total variance as adaptivity measure
        }
    
    return allocation_analysis

def plot_comparison(results, save_path="results/comparison_analysis.png"):
    """Create comprehensive comparison plots"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. Total QoS comparison
    policies = list(results.keys())
    total_qos = [results[p]['total_qos'] for p in policies]
    
    axes[0, 0].bar(policies, total_qos, color=['skyblue', 'lightcoral', 'lightgreen'])
    axes[0, 0].set_title('Total QoS Comparison')
    axes[0, 0].set_ylabel('Total QoS')
    axes[0, 0].tick_params(axis='x', rotation=45)
    
    # 2. QoS over time
    for policy in policies:
        axes[0, 1].plot(results[policy]['qos_history'], label=policy, marker='o')
    axes[0, 1].set_title('QoS Over Time')
    axes[0, 1].set_xlabel('Decision Period')
    axes[0, 1].set_ylabel('QoS per Period')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. Average allocation comparison
    slice_types = ['URLLC', 'eMBB', 'mMTC']
    x = np.arange(len(slice_types))
    width = 0.25
    
    for i, policy in enumerate(policies):
        decisions = results[policy]['decision_summary']
        avg_allocation = [np.mean([d['allocation'][s] for d in decisions]) for s in slice_types]
        axes[0, 2].bar(x + i * width, avg_allocation, width, label=policy)
    
    axes[0, 2].set_title('Average RB Allocation by Slice')
    axes[0, 2].set_xlabel('Slice Type')
    axes[0, 2].set_ylabel('Average RBs')
    axes[0, 2].set_xticks(x + width)
    axes[0, 2].set_xticklabels(slice_types)
    axes[0, 2].legend()
    
    # 4. Completed tasks
    completed_tasks = [results[p]['completed_tasks'] for p in policies]
    axes[1, 0].bar(policies, completed_tasks, color=['skyblue', 'lightcoral', 'lightgreen'])
    axes[1, 0].set_title('Completed Tasks')
    axes[1, 0].set_ylabel('Number of Tasks')
    axes[1, 0].tick_params(axis='x', rotation=45)
    
    # 5. QoS efficiency
    qos_efficiency = [results[p]['total_qos'] / (50 * 10) for p in policies]
    axes[1, 1].bar(policies, qos_efficiency, color=['skyblue', 'lightcoral', 'lightgreen'])
    axes[1, 1].set_title('QoS Efficiency (QoS per RB per Decision)')
    axes[1, 1].set_ylabel('Efficiency')
    axes[1, 1].tick_params(axis='x', rotation=45)
    
    # 6. Allocation adaptivity (standard deviation)
    allocation_analysis = analyze_allocation_patterns(results)
    adaptivity = [allocation_analysis[p]['adaptivity'] for p in policies]
    axes[1, 2].bar(policies, adaptivity, color=['skyblue', 'lightcoral', 'lightgreen'])
    axes[1, 2].set_title('Allocation Adaptivity (Total Std Dev)')
    axes[1, 2].set_ylabel('Adaptivity Score')
    axes[1, 2].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()

def generate_report(results):
    """Generate comprehensive text report"""
    print("=" * 80)
    print("Q2 DYNAMIC RESOURCE ALLOCATION - SIMULATION RESULTS")
    print("=" * 80)
    
    # Summary table
    summary_df = create_summary_table(results)
    print("\n📊 PERFORMANCE SUMMARY")
    print("-" * 50)
    print(summary_df.to_string(index=False, float_format='%.3f'))
    
    # Best performing policy
    best_policy = summary_df.iloc[0]['Policy']
    best_qos = summary_df.iloc[0]['Total QoS']
    print(f"\n🏆 BEST PERFORMING POLICY: {best_policy}")
    print(f"   Total QoS: {best_qos:.3f}")
    
    # Allocation analysis
    allocation_analysis = analyze_allocation_patterns(results)
    print(f"\n📈 ALLOCATION PATTERNS")
    print("-" * 50)
    
    for policy, analysis in allocation_analysis.items():
        avg_alloc = analysis['avg_allocation']
        adaptivity = analysis['adaptivity']
        print(f"\n{policy}:")
        print(f"  Average Allocation: URLLC={avg_alloc['URLLC']:.1f}, eMBB={avg_alloc['eMBB']:.1f}, mMTC={avg_alloc['mMTC']:.1f}")
        print(f"  Adaptivity Score: {adaptivity:.2f}")
    
    # Key insights
    print(f"\n💡 KEY INSIGHTS")
    print("-" * 50)
    
    # Find most adaptive policy
    most_adaptive = max(allocation_analysis.keys(), key=lambda p: allocation_analysis[p]['adaptivity'])
    least_adaptive = min(allocation_analysis.keys(), key=lambda p: allocation_analysis[p]['adaptivity'])
    
    print(f"• Most Adaptive Policy: {most_adaptive}")
    print(f"• Least Adaptive Policy: {least_adaptive}")
    
    # QoS consistency
    qos_consistency = {}
    for policy, data in results.items():
        qos_consistency[policy] = np.std(data['qos_history'])
    
    most_consistent = min(qos_consistency.keys(), key=lambda p: qos_consistency[p])
    print(f"• Most Consistent QoS: {most_consistent} (std={qos_consistency[most_consistent]:.3f})")
    
    # Task completion efficiency
    completion_efficiency = {}
    for policy, data in results.items():
        completion_efficiency[policy] = data['completed_tasks'] / data['total_qos'] if data['total_qos'] > 0 else 0
    
    most_efficient = max(completion_efficiency.keys(), key=lambda p: completion_efficiency[p])
    print(f"• Most Task-Efficient: {most_efficient} (tasks/QoS={completion_efficiency[most_efficient]:.3f})")
    
    print(f"\n📋 RECOMMENDATIONS")
    print("-" * 50)
    print("• For maximum QoS: Use the best performing policy")
    print("• For stable performance: Use the most consistent policy")
    print("• For dynamic environments: Use the most adaptive policy")
    print("• For task completion: Use the most task-efficient policy")

def main():
    """Main analysis function"""
    print("Loading Q2 simulation results...")
    
    results = load_results()
    
    if not results:
        print("No results found in 'results/' directory. Run main.py first.")
        return
    
    print(f"Found results for {len(results)} policies: {list(results.keys())}")
    
    # Generate comprehensive report
    generate_report(results)
    
    # Create comparison plots
    print(f"\nGenerating comparison plots...")
    plot_comparison(results)
    
    print(f"\nAnalysis complete! Check 'results/comparison_analysis.png' for visualizations.")

if __name__ == '__main__':
    main()
