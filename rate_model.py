# rate_model.py - Signal and Rate Calculation Models
"""
Implement signal transmission and rate calculation based on Appendix.md
"""
import numpy as np
import config

class RateCalculator:
    """Calculate transmission rates based on channel conditions"""
    
    def __init__(self, tx_power_dbm: float = config.DEFAULT_TX_POWER_DBM):
        self.tx_power_dbm = tx_power_dbm
        self.tx_power_mw = config.dbm_to_mw(tx_power_dbm)
        
    def calculate_noise_power(self, num_rb: int) -> float:
        """Calculate noise power in mW for given number of RBs"""
        # N0 = -174 + 10*log10(i*b) + NF (dBm)
        bandwidth_hz = num_rb * config.RB_BANDWIDTH_KHZ * 1000
        noise_dbm = config.NOISE_FLOOR_DBM + 10 * np.log10(bandwidth_hz) + config.NOISE_FIGURE_DB
        return config.dbm_to_mw(noise_dbm)
    
    def calculate_received_power(self, tx_power_dbm: float, path_loss_db: float, 
                               small_scale_linear: float) -> float:
        """Calculate received signal power in mW"""
        # P_rx = 10^((P_tx - path_loss)/10) * |h|^2
        rx_power_mw = config.dbm_to_mw(tx_power_dbm - path_loss_db) * small_scale_linear
        return rx_power_mw
    
    def calculate_sinr(self, user_id: str, path_loss_db: float, small_scale_linear: float,
                      num_rb: int, interference_power_mw: float = 0.0) -> float:
        """Calculate SINR for a user"""
        # Signal power
        signal_power = self.calculate_received_power(self.tx_power_dbm, path_loss_db, small_scale_linear)
        
        # Noise power
        noise_power = self.calculate_noise_power(num_rb)
        
        # SINR = S / (I + N)
        sinr_linear = signal_power / (interference_power_mw + noise_power)
        return sinr_linear
    
    def calculate_rate_mbps(self, sinr_linear: float, num_rb: int) -> float:
        """Calculate transmission rate in Mbps using Shannon formula"""
        # r = i * b * log(1 + γ) where b is in Hz, result in bps
        bandwidth_hz = num_rb * config.RB_BANDWIDTH_KHZ * 1000
        rate_bps = bandwidth_hz * np.log2(1 + sinr_linear)
        rate_mbps = rate_bps / 1e6  # Convert to Mbps
        return rate_mbps
    
    def calculate_user_rate(self, user_id: str, user_type: str, path_loss_db: float,
                           small_scale_linear: float, num_rb_allocated: int) -> float:
        """Calculate rate for a specific user given RB allocation"""
        if num_rb_allocated == 0:
            return 0.0
            
        # Get RB requirement per user for this type
        rb_per_user = config.USER_TYPES[user_type]['rb_per_user']
        
        # Calculate how many users can be served concurrently
        concurrent_users = num_rb_allocated // rb_per_user
        if concurrent_users == 0:
            return 0.0
            
        # Each user gets rb_per_user RBs
        sinr = self.calculate_sinr(user_id, path_loss_db, small_scale_linear, rb_per_user)
        rate = self.calculate_rate_mbps(sinr, rb_per_user)
        
        return rate

class QoSCalculator:
    """Calculate Quality of Service metrics"""
    
    @staticmethod
    def calculate_urllc_qos(delay_ms: float, sla_delay_ms: float, alpha: float = 0.95, penalty: float = 5) -> float:
        """Calculate URLLC QoS based on delay"""
        if delay_ms <= sla_delay_ms:
            return alpha ** delay_ms
        else:
            return -penalty
    
    @staticmethod
    def calculate_embb_qos(rate_mbps: float, delay_ms: float, sla_rate_mbps: float, 
                          sla_delay_ms: float, penalty: float = 3) -> float:
        """Calculate eMBB QoS based on rate and delay"""
        if delay_ms > sla_delay_ms:
            return -penalty
        elif rate_mbps >= sla_rate_mbps:
            return 1.0
        else:
            return rate_mbps / sla_rate_mbps
    
    @staticmethod
    def calculate_mmtc_qos(served_users: int, total_users: int, delay_ms: float,
                          sla_delay_ms: float, penalty: float = 1) -> float:
        """Calculate mMTC QoS based on access ratio"""
        if delay_ms > sla_delay_ms:
            return -penalty
        elif total_users == 0:
            return 0.0
        else:
            return served_users / total_users
    
    @staticmethod
    def calculate_task_qos(user_type: str, rate_mbps: float, delay_ms: float, 
                          served_users: int = 1, total_users: int = 1) -> float:
        """Calculate QoS for a task based on user type"""
        params = config.USER_TYPES[user_type]
        
        if user_type == 'URLLC':
            return QoSCalculator.calculate_urllc_qos(
                delay_ms, params['sla_delay_ms'], params['alpha'], params['penalty']
            )
        elif user_type == 'eMBB':
            return QoSCalculator.calculate_embb_qos(
                rate_mbps, delay_ms, params['sla_rate_mbps'], 
                params['sla_delay_ms'], params['penalty']
            )
        elif user_type == 'mMTC':
            return QoSCalculator.calculate_mmtc_qos(
                served_users, total_users, delay_ms, 
                params['sla_delay_ms'], params['penalty']
            )
        else:
            raise ValueError(f"Unknown user type: {user_type}")
