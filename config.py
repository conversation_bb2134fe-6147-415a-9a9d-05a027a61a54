# config.py - Q2 Configuration Parameters
"""
Configuration for Q2 Dynamic Resource Allocation Problem
Based on Appendix.md and Topic.md specifications
"""
import numpy as np

# Time parameters
TOTAL_TIME_MS = 1000  # Total simulation time
DECISION_INTERVAL_MS = 100  # Decision every 100ms
SUBFRAME_DURATION_MS = 10  # Each subframe is 10ms
NUM_DECISIONS = TOTAL_TIME_MS // DECISION_INTERVAL_MS  # 10 decisions
SUBFRAMES_PER_DECISION = DECISION_INTERVAL_MS // SUBFRAME_DURATION_MS  # 10 subframes

# Resource parameters
TOTAL_RB = 50  # Total resource blocks per base station
RB_BANDWIDTH_KHZ = 360  # Each RB bandwidth in kHz

# User type parameters (from Appendix Table 1)
USER_TYPES = {
    'URLLC': {
        'rb_per_user': 10,
        'sla_rate_mbps': 10,
        'sla_delay_ms': 5,
        'task_size_range': (0.01, 0.012),  # Mbit
        'penalty': 5,
        'alpha': 0.95  # URLLC decay factor
    },
    'eMBB': {
        'rb_per_user': 5,
        'sla_rate_mbps': 50,
        'sla_delay_ms': 100,
        'task_size_range': (0.1, 0.12),  # Mbit
        'penalty': 3
    },
    'mMTC': {
        'rb_per_user': 2,
        'sla_rate_mbps': 1,
        'sla_delay_ms': 500,
        'task_size_range': (0.012, 0.014),  # Mbit
        'penalty': 1
    }
}

# Channel and noise parameters
NOISE_FLOOR_DBM = -174  # Thermal noise density dBm/Hz
NOISE_FIGURE_DB = 7  # Receiver noise figure
DEFAULT_TX_POWER_DBM = 20  # Default transmit power if not specified

# MPC parameters
MPC_HORIZON = 3  # Look-ahead horizon for MPC (3-5 recommended)
MAX_ITERATIONS = 100  # Max iterations for optimization

# File paths
DATA_PATH = "Solution/Q2/channel_data.xlsx"
OUTPUT_DIR = "results"

# Utility functions for unit conversion
def dbm_to_mw(dbm):
    """Convert dBm to mW"""
    return 10 ** ((dbm - 30) / 10)

def db_to_linear(db):
    """Convert dB to linear scale"""
    return 10 ** (db / 10)

def mw_to_dbm(mw):
    """Convert mW to dBm"""
    return 30 + 10 * np.log10(mw)

# Validation
assert sum(USER_TYPES[t]['rb_per_user'] for t in USER_TYPES) <= TOTAL_RB * 3  # Sanity check
