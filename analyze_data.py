#!/usr/bin/env python3
# analyze_data.py - 仔细分析Excel数据的真实含义
import pandas as pd
import numpy as np

def analyze_excel_data():
    """仔细分析Excel数据结构和含义"""
    excel_path = 'Solution/Q2/channel_data.xlsx'
    sheets = pd.read_excel(excel_path, sheet_name=None, engine='openpyxl')
    
    print('=== 详细分析Excel数据结构 ===')
    
    # 分析任务流数据
    task_sheet = sheets['用户任务流']
    print('\n任务流数据分析:')
    print(f'时间范围: {task_sheet["Time"].min():.3f} 到 {task_sheet["Time"].max():.3f} 秒')
    print(f'数据点数: {len(task_sheet)}')
    print(f'时间间隔: {task_sheet["Time"].iloc[1] - task_sheet["Time"].iloc[0]:.6f} 秒')
    
    # 检查每个用户的任务数据特征
    print('\n各用户任务数据特征:')
    for user in ['U1', 'U2', 'e1', 'e2', 'e3', 'e4', 'm1', 'm2', 'm3', 'm4', 'm5']:
        if user in task_sheet.columns:
            data = task_sheet[user].values
            non_zero = data[data > 0]
            zero_count = len(data[data == 0])
            if len(non_zero) > 0:
                print(f'{user}: 非零值{len(non_zero)}个, 零值{zero_count}个, 最大值{non_zero.max():.4f}, 平均值{non_zero.mean():.4f}')
            else:
                print(f'{user}: 全部为零')
    
    # 分析时间分布 - 关键：理解数据的真实含义
    print('\n前20个时间点的任务情况:')
    for i in range(20):
        time_point = task_sheet.iloc[i]['Time']
        tasks_at_time = []
        for user in ['U1', 'U2', 'e1', 'e2', 'e3', 'e4', 'm1', 'm2']:
            if user in task_sheet.columns and task_sheet.iloc[i][user] > 0:
                tasks_at_time.append(f'{user}:{task_sheet.iloc[i][user]:.4f}')
        if tasks_at_time:
            print(f'时间{time_point:.3f}s: {tasks_at_time}')
    
    # 分析决策时刻的任务情况
    print('\n决策时刻(每100ms)的任务情况:')
    decision_times_s = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
    
    for decision_time in decision_times_s:
        # 找到最接近的时间点
        time_diff = np.abs(task_sheet['Time'] - decision_time)
        closest_idx = time_diff.argmin()
        actual_time = task_sheet.iloc[closest_idx]['Time']
        
        print(f'\n决策时刻 {decision_time:.1f}s (实际{actual_time:.3f}s):')
        total_tasks = 0
        for user in ['U1', 'U2', 'e1', 'e2', 'e3', 'e4', 'm1', 'm2', 'm3', 'm4', 'm5']:
            if user in task_sheet.columns:
                task_size = task_sheet.iloc[closest_idx][user]
                if task_size > 0:
                    print(f'  {user}: {task_size:.4f} Mbit')
                    total_tasks += 1
        print(f'  总任务数: {total_tasks}')
    
    # 分析信道数据
    print('\n=== 信道数据分析 ===')
    path_loss_sheet = sheets['大规模衰减']
    small_scale_sheet = sheets['小规模瑞丽衰减']
    
    print('\n路径损耗数据 (dB):')
    for user in ['U1', 'U2', 'e1', 'e2']:
        if user in path_loss_sheet.columns:
            data = path_loss_sheet[user].values
            print(f'{user}: 范围 {data.min():.1f} 到 {data.max():.1f} dB, 平均 {data.mean():.1f} dB')
    
    print('\n小尺度衰落数据 (dB):')
    for user in ['U1', 'U2', 'e1', 'e2']:
        if user in small_scale_sheet.columns:
            data = small_scale_sheet[user].values
            print(f'{user}: 范围 {data.min():.1f} 到 {data.max():.1f} dB, 平均 {data.mean():.1f} dB')

if __name__ == '__main__':
    analyze_excel_data()
