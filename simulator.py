# simulator.py - Q2 System Simulator
"""
Simulate the dynamic resource allocation system with queues and subframes
"""
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, field
from collections import deque
import config
from rate_model import RateCalculator, QoSCalculator

@dataclass
class Task:
    """Represents a user task"""
    user_id: str
    user_type: str
    arrival_time_ms: float
    data_size_mbit: float
    remaining_data_mbit: float = field(init=False)
    start_time_ms: Optional[float] = None
    completion_time_ms: Optional[float] = None
    
    def __post_init__(self):
        self.remaining_data_mbit = self.data_size_mbit
    
    @property
    def is_completed(self) -> bool:
        return self.remaining_data_mbit <= 0
    
    @property
    def delay_ms(self) -> float:
        if self.completion_time_ms is not None:
            return self.completion_time_ms - self.arrival_time_ms
        return 0.0

@dataclass
class UserQueue:
    """Queue for a specific user"""
    user_id: str
    user_type: str
    tasks: deque = field(default_factory=deque)
    
    def add_task(self, task: Task):
        self.tasks.append(task)
    
    def get_next_task(self) -> Optional[Task]:
        return self.tasks[0] if self.tasks else None
    
    def remove_completed_tasks(self):
        while self.tasks and self.tasks[0].is_completed:
            self.tasks.popleft()

class Q2Simulator:
    """Main simulator for Q2 problem"""
    
    def __init__(self, data_loader, tx_power_dbm: float = config.DEFAULT_TX_POWER_DBM):
        self.data_loader = data_loader
        self.rate_calculator = RateCalculator(tx_power_dbm)
        self.qos_calculator = QoSCalculator()
        
        # Initialize user queues
        self.user_queues = {}
        for user_id in data_loader.users:
            user_type = data_loader.get_user_type(user_id)
            self.user_queues[user_id] = UserQueue(user_id, user_type)
        
        # Simulation state
        self.current_time_ms = 0.0
        self.completed_tasks = []
        self.total_qos = 0.0
        
        # Statistics
        self.decision_history = []
        self.qos_history = []
        
    def generate_task_arrivals_at_decision_time(self, decision_time_ms: float) -> List[Task]:
        """Generate task arrivals exactly at decision time based on Excel data"""
        new_tasks = []

        for user_id in self.data_loader.users:
            # Get task size from Excel data at exact decision time
            task_size = self.data_loader.get_task_arrivals_at_time(user_id, decision_time_ms)

            # If task size > 0, create a task
            if task_size > 0:
                user_type = self.data_loader.get_user_type(user_id)

                task = Task(
                    user_id=user_id,
                    user_type=user_type,
                    arrival_time_ms=decision_time_ms,
                    data_size_mbit=task_size
                )
                new_tasks.append(task)

        return new_tasks
    
    def get_slice_capacity(self, rb_allocation: Dict[str, int]) -> Dict[str, int]:
        """Calculate concurrent capacity for each slice"""
        capacity = {}
        for slice_type in ['URLLC', 'eMBB', 'mMTC']:
            rb_allocated = rb_allocation.get(slice_type, 0)
            rb_per_user = config.USER_TYPES[slice_type]['rb_per_user']
            capacity[slice_type] = rb_allocated // rb_per_user
        return capacity
    
    def select_users_for_service(self, slice_type: str, capacity: int) -> List[str]:
        """Select users for service in a slice (FIFO, number-priority)"""
        eligible_users = []
        
        # Find users of this type with pending tasks
        for user_id, queue in self.user_queues.items():
            if queue.user_type == slice_type and queue.get_next_task() is not None:
                eligible_users.append(user_id)
        
        # Sort by user ID (number priority as mentioned in Appendix)
        eligible_users.sort()
        
        # Return up to capacity users
        return eligible_users[:capacity]
    
    def simulate_subframe(self, rb_allocation: Dict[str, int], subframe_duration_ms: float = config.SUBFRAME_DURATION_MS):
        """Simulate one subframe (10ms) of transmission"""
        capacity = self.get_slice_capacity(rb_allocation)
        completed_tasks_this_subframe = []

        for slice_type in ['URLLC', 'eMBB', 'mMTC']:
            if capacity[slice_type] == 0:
                continue

            # Select users for service
            serving_users = self.select_users_for_service(slice_type, capacity[slice_type])

            for user_id in serving_users:
                queue = self.user_queues[user_id]
                task = queue.get_next_task()

                if task is None:
                    continue

                # Mark start time if not started
                if task.start_time_ms is None:
                    task.start_time_ms = self.current_time_ms

                # Get channel conditions
                path_loss, small_scale = self.data_loader.get_channel_at_time(user_id, self.current_time_ms)

                # Calculate transmission rate
                rb_per_user = config.USER_TYPES[slice_type]['rb_per_user']
                rate_mbps = self.rate_calculator.calculate_user_rate(
                    user_id, slice_type, path_loss, small_scale, rb_per_user
                )

                # Calculate data transmitted in this subframe
                data_transmitted = rate_mbps * subframe_duration_ms / 1000  # Mbit
                task.remaining_data_mbit = max(0, task.remaining_data_mbit - data_transmitted)

                # Check if task is completed
                if task.is_completed:
                    task.completion_time_ms = self.current_time_ms + subframe_duration_ms
                    task.final_rate_mbps = rate_mbps  # Store final rate for QoS calculation
                    completed_tasks_this_subframe.append(task)
                    self.completed_tasks.append(task)

                    # Remove from queue immediately to avoid double counting
                    queue.remove_completed_tasks()

        return completed_tasks_this_subframe
    
    def simulate_decision_period(self, rb_allocation: Dict[str, int]) -> float:
        """Simulate one decision period (100ms = 10 subframes)"""
        period_completed_tasks = []

        for subframe in range(config.SUBFRAMES_PER_DECISION):
            completed_tasks = self.simulate_subframe(rb_allocation)
            period_completed_tasks.extend(completed_tasks)
            self.current_time_ms += config.SUBFRAME_DURATION_MS

        # Calculate QoS for this period
        period_qos = self.calculate_period_qos(period_completed_tasks)
        return period_qos

    def calculate_period_qos(self, completed_tasks: List) -> float:
        """Calculate QoS for completed tasks in this period"""
        total_qos = 0.0

        # Group tasks by type
        tasks_by_type = {'URLLC': [], 'eMBB': [], 'mMTC': []}
        for task in completed_tasks:
            user_type = self.data_loader.get_user_type(task.user_id)
            tasks_by_type[user_type].append(task)

        # Calculate QoS for URLLC and eMBB (individual task QoS)
        for slice_type in ['URLLC', 'eMBB']:
            for task in tasks_by_type[slice_type]:
                rate_mbps = getattr(task, 'final_rate_mbps', 0.0)
                task_qos = self.qos_calculator.calculate_task_qos(
                    slice_type, rate_mbps, task.delay_ms
                )
                total_qos += task_qos

        # Calculate QoS for mMTC (access ratio for this period)
        mmtc_tasks = tasks_by_type['mMTC']
        if mmtc_tasks:
            # Count total mMTC users with tasks in this period
            mmtc_users_with_tasks = set()
            for user_id, queue in self.user_queues.items():
                if queue.user_type == 'mMTC' and len(queue.tasks) > 0:
                    mmtc_users_with_tasks.add(user_id)

            # Add users who completed tasks this period
            for task in mmtc_tasks:
                mmtc_users_with_tasks.add(task.user_id)

            total_mmtc_users = len(mmtc_users_with_tasks)
            served_mmtc_users = len(set(task.user_id for task in mmtc_tasks))

            if total_mmtc_users > 0:
                # Calculate average delay for mMTC tasks
                avg_delay = sum(task.delay_ms for task in mmtc_tasks) / len(mmtc_tasks)
                mmtc_qos = self.qos_calculator.calculate_mmtc_qos(
                    served_mmtc_users, total_mmtc_users, avg_delay,
                    config.USER_TYPES['mMTC']['sla_delay_ms'],
                    config.USER_TYPES['mMTC']['penalty']
                )
                total_qos += mmtc_qos

        return total_qos
    
    def add_new_arrivals_at_decision_time(self, decision_time_ms: float):
        """Add new task arrivals to queues at exact decision time"""
        new_tasks = self.generate_task_arrivals_at_decision_time(decision_time_ms)

        for task in new_tasks:
            self.user_queues[task.user_id].add_task(task)
    
    def get_queue_state(self) -> Dict:
        """Get current queue state for decision making"""
        state = {}
        
        for user_id, queue in self.user_queues.items():
            state[user_id] = {
                'user_type': queue.user_type,
                'queue_length': len(queue.tasks),
                'next_task_size': queue.get_next_task().remaining_data_mbit if queue.get_next_task() else 0,
                'next_task_age': self.current_time_ms - queue.get_next_task().arrival_time_ms if queue.get_next_task() else 0
            }
            
        return state
    
    def run_simulation(self, decision_policy) -> Dict:
        """Run complete simulation with given decision policy"""
        print("Starting Q2 simulation...")
        
        self.current_time_ms = 0.0
        self.total_qos = 0.0
        self.completed_tasks = []
        self.decision_history = []
        self.qos_history = []
        
        for decision_step in range(config.NUM_DECISIONS):
            decision_time = decision_step * config.DECISION_INTERVAL_MS

            # Add new arrivals exactly at decision time (based on Excel data)
            self.add_new_arrivals_at_decision_time(decision_time)
            
            # Get current state
            state = self.get_queue_state()
            
            # Make decision
            rb_allocation = decision_policy.make_decision(state, decision_time, self.data_loader)
            
            # Validate allocation
            total_rb = sum(rb_allocation.values())
            if total_rb != config.TOTAL_RB:
                print(f"Warning: RB allocation sum {total_rb} != {config.TOTAL_RB}")
            
            # Simulate this decision period
            period_qos = self.simulate_decision_period(rb_allocation)
            self.total_qos += period_qos
            
            # Record history
            self.decision_history.append({
                'time_ms': decision_time,
                'allocation': rb_allocation.copy(),
                'qos': period_qos,
                'queue_state': state
            })
            self.qos_history.append(period_qos)
            
            print(f"Decision {decision_step}: {rb_allocation}, QoS: {period_qos:.3f}")
        
        print(f"Simulation complete. Total QoS: {self.total_qos:.3f}")
        
        return {
            'total_qos': self.total_qos,
            'completed_tasks': len(self.completed_tasks),
            'decision_history': self.decision_history,
            'qos_history': self.qos_history
        }
