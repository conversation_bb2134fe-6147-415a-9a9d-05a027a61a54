#!/usr/bin/env python3
# main.py - Main script for Q2 solver
"""
Main entry point for Q2 Dynamic Resource Allocation solver
"""
import argparse
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import json

import config
from data_loader import Q2DataLoader
from simulator import Q2Simulator
from policies import GreedyPolicy, MPCPolicy, FixedRatioPolicy

def setup_output_dir():
    """Create output directory for results"""
    output_dir = Path(config.OUTPUT_DIR)
    output_dir.mkdir(exist_ok=True)
    return output_dir

def plot_results(results: dict, policy_name: str, output_dir: Path):
    """Plot simulation results"""
    
    # QoS over time
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.plot(results['qos_history'])
    plt.title(f'QoS per Decision Period - {policy_name}')
    plt.xlabel('Decision Period')
    plt.ylabel('QoS')
    plt.grid(True)
    
    # RB allocation over time
    plt.subplot(2, 2, 2)
    decisions = results['decision_history']
    times = [d['time_ms'] for d in decisions]
    urllc_rb = [d['allocation']['URLLC'] for d in decisions]
    embb_rb = [d['allocation']['eMBB'] for d in decisions]
    mmtc_rb = [d['allocation']['mMTC'] for d in decisions]
    
    plt.stackplot(times, urllc_rb, embb_rb, mmtc_rb, 
                 labels=['URLLC', 'eMBB', 'mMTC'], alpha=0.7)
    plt.title(f'RB Allocation Over Time - {policy_name}')
    plt.xlabel('Time (ms)')
    plt.ylabel('Resource Blocks')
    plt.legend()
    plt.grid(True)
    
    # Cumulative QoS
    plt.subplot(2, 2, 3)
    cumulative_qos = np.cumsum(results['qos_history'])
    plt.plot(times, cumulative_qos)
    plt.title(f'Cumulative QoS - {policy_name}')
    plt.xlabel('Time (ms)')
    plt.ylabel('Cumulative QoS')
    plt.grid(True)
    
    # Queue lengths over time (if available)
    plt.subplot(2, 2, 4)
    if decisions:
        queue_lengths = []
        for d in decisions:
            total_queue = sum(state['queue_length'] for state in d['queue_state'].values())
            queue_lengths.append(total_queue)
        
        plt.plot(times, queue_lengths)
        plt.title(f'Total Queue Length - {policy_name}')
        plt.xlabel('Time (ms)')
        plt.ylabel('Total Tasks in Queue')
        plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(output_dir / f'results_{policy_name.lower().replace(" ", "_")}.png', dpi=300, bbox_inches='tight')
    plt.show()

def save_results(results: dict, policy_name: str, output_dir: Path):
    """Save results to JSON file"""
    
    # Prepare serializable results
    serializable_results = {
        'policy': policy_name,
        'total_qos': results['total_qos'],
        'completed_tasks': results['completed_tasks'],
        'qos_history': results['qos_history'],
        'decision_summary': []
    }
    
    for decision in results['decision_history']:
        serializable_results['decision_summary'].append({
            'time_ms': decision['time_ms'],
            'allocation': decision['allocation'],
            'qos': decision['qos'],
            'total_queue_length': sum(state['queue_length'] for state in decision['queue_state'].values())
        })
    
    filename = output_dir / f'results_{policy_name.lower().replace(" ", "_")}.json'
    with open(filename, 'w') as f:
        json.dump(serializable_results, f, indent=2)
    
    print(f"Results saved to {filename}")

def compare_policies(results_list: list, output_dir: Path):
    """Compare multiple policies"""
    
    plt.figure(figsize=(15, 10))
    
    # Total QoS comparison
    plt.subplot(2, 3, 1)
    policies = [r['policy'] for r in results_list]
    total_qos = [r['total_qos'] for r in results_list]
    
    plt.bar(policies, total_qos)
    plt.title('Total QoS Comparison')
    plt.ylabel('Total QoS')
    plt.xticks(rotation=45)
    
    # QoS over time comparison
    plt.subplot(2, 3, 2)
    for result in results_list:
        plt.plot(result['qos_history'], label=result['policy'])
    plt.title('QoS Over Time Comparison')
    plt.xlabel('Decision Period')
    plt.ylabel('QoS per Period')
    plt.legend()
    plt.grid(True)
    
    # Average allocation comparison
    plt.subplot(2, 3, 3)
    slice_types = ['URLLC', 'eMBB', 'mMTC']
    x = np.arange(len(slice_types))
    width = 0.25
    
    for i, result in enumerate(results_list):
        decisions = result['decision_summary']
        avg_allocation = {}
        for slice_type in slice_types:
            avg_allocation[slice_type] = np.mean([d['allocation'][slice_type] for d in decisions])
        
        plt.bar(x + i * width, [avg_allocation[s] for s in slice_types], 
               width, label=result['policy'])
    
    plt.title('Average RB Allocation Comparison')
    plt.xlabel('Slice Type')
    plt.ylabel('Average RBs')
    plt.xticks(x + width, slice_types)
    plt.legend()
    
    # Completed tasks comparison
    plt.subplot(2, 3, 4)
    completed_tasks = [r['completed_tasks'] for r in results_list]
    plt.bar(policies, completed_tasks)
    plt.title('Completed Tasks Comparison')
    plt.ylabel('Number of Completed Tasks')
    plt.xticks(rotation=45)
    
    # QoS efficiency (QoS per RB)
    plt.subplot(2, 3, 5)
    qos_efficiency = [r['total_qos'] / config.TOTAL_RB / config.NUM_DECISIONS for r in results_list]
    plt.bar(policies, qos_efficiency)
    plt.title('QoS Efficiency (QoS per RB per Decision)')
    plt.ylabel('QoS Efficiency')
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    plt.savefig(output_dir / 'policy_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

def run_single_policy(policy, data_loader, output_dir: Path, plot: bool = True):
    """Run simulation with a single policy"""
    
    print(f"\n=== Running {policy.name} Policy ===")
    
    # Create simulator
    simulator = Q2Simulator(data_loader)
    
    # Run simulation
    results = simulator.run_simulation(policy)
    
    # Print summary
    print(f"\nResults for {policy.name}:")
    print(f"  Total QoS: {results['total_qos']:.3f}")
    print(f"  Completed Tasks: {results['completed_tasks']}")
    print(f"  Average QoS per Decision: {results['total_qos']/config.NUM_DECISIONS:.3f}")
    
    # Save results
    save_results(results, policy.name, output_dir)
    
    # Plot results
    if plot:
        plot_results(results, policy.name, output_dir)
    
    return {
        'policy': policy.name,
        'total_qos': results['total_qos'],
        'completed_tasks': results['completed_tasks'],
        'qos_history': results['qos_history'],
        'decision_summary': [
            {
                'time_ms': d['time_ms'],
                'allocation': d['allocation'],
                'qos': d['qos'],
                'total_queue_length': sum(state['queue_length'] for state in d['queue_state'].values())
            }
            for d in results['decision_history']
        ]
    }

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Q2 Dynamic Resource Allocation Solver')
    parser.add_argument('--data', default=config.DATA_PATH, help='Path to Excel data file')
    parser.add_argument('--policy', choices=['greedy', 'mpc', 'fixed', 'all'], default='all',
                       help='Policy to run')
    parser.add_argument('--no-plot', action='store_true', help='Disable plotting')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    
    args = parser.parse_args()
    
    # Set random seed
    np.random.seed(args.seed)
    
    # Setup
    output_dir = setup_output_dir()
    print(f"Output directory: {output_dir}")
    
    # Load data
    print("Loading data...")
    data_loader = Q2DataLoader(args.data)
    data_loader.load_and_parse()
    
    # Define policies
    policies = {
        'greedy': GreedyPolicy(),
        'mpc': MPCPolicy(horizon=config.MPC_HORIZON),
        'fixed': FixedRatioPolicy()
    }
    
    # Run simulations
    results_list = []
    
    if args.policy == 'all':
        for policy_name, policy in policies.items():
            result = run_single_policy(policy, data_loader, output_dir, not args.no_plot)
            results_list.append(result)
    else:
        policy = policies[args.policy]
        result = run_single_policy(policy, data_loader, output_dir, not args.no_plot)
        results_list.append(result)
    
    # Compare policies if multiple were run
    if len(results_list) > 1 and not args.no_plot:
        print("\n=== Policy Comparison ===")
        compare_policies(results_list, output_dir)
    
    print(f"\nSimulation complete. Results saved in {output_dir}")

if __name__ == '__main__':
    main()
